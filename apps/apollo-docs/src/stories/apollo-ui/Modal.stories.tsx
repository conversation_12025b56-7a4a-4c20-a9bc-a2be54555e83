import { useState } from "react"
import { ComponentRules, CSSClassesTable, MultiplePropsTable, UsageGuidelines } from "@/components"
import { Button, Input, Modal, Typography } from "@apollo/ui"
import {
  CheckCircle,
  ExclamationCircle,
  InfoCircle,
  Warning,
} from "@design-systems/apollo-icons"
import {
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"
// Images are served from public directory
const actionDo = "/assets/apollo-ui/modal/action-do.png"
const actionDont = "/assets/apollo-ui/modal/action-dont.png"

/**
 * Modal component
 *
 * The Modal component provides a versatile dialog overlay for displaying content that requires
 * user attention or interaction. It supports various configurations including different sizes,
 * dismissible behavior, and compositional structure with header, content, and footer sections.
 *
 * Notes:
 * - Uses a compositional pattern with Modal.Root, Modal.Header, Modal.Content, Modal.Footer, and Modal.CloseButton
 * - Built on top of Base UI Dialog for accessibility
 * - Supports keyboard navigation and focus management
 * - Provides backdrop click and ESC key dismissal options
 * - Responsive design with mobile-friendly layouts
 */
const meta: Meta<typeof Modal.Root> = {
  title: "@apollo∕ui/Components/Feedback/Modal",
  component: Modal.Root,
  subcomponents: {
    ModalHeader: Modal.Header,
    ModalContent: Modal.Content,
    ModalFooter: Modal.Footer,
    ModalCloseButton: Modal.CloseButton,
  },
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2540-12910&m=dev",
    },
    controls: { expanded: true },
    docs: {
      description: {
        component:
          "The Modal component is a versatile dialog overlay used to display content that requires user attention or interaction. It provides a focused experience by overlaying content on top of the main interface, helping users complete specific tasks without losing context.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Modal } from "@apollo/ui"`} language="tsx" />
          <h3>Usage</h3>
          <UsageGuidelines
            guidelines={[
              <>
                <code>Modal.Root</code>: The root component that wraps the
                entire modal and manages open/close state.
              </>,
              <>
                <code>Modal.Header</code>: The header section of the modal,
                typically containing a title, optional icon, and close button.
              </>,
              <>
                <code>Modal.Content</code>: The main content area of the modal
                where you place your primary content. Supports scrollable
                content when it overflows.
              </>,
              <>
                <code>Modal.Footer</code>: The footer section, typically
                containing action buttons like Cancel, Confirm, Delete, etc.
              </>,
              <>
                <code>Modal.CloseButton</code>: A button that closes the modal
                when clicked, usually placed in the header.
              </>,
            ]}
          />
          <h2 id="modal-props">Props</h2>
          <MultiplePropsTable
            tabs={[
              {
                label: "Modal.Root",
                props: [
                  {
                    name: "open",
                    description: "Controls whether the modal is open or closed.",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "onOpenChange",
                    description: "Callback fired when the open state changes.",
                    type: "(open: boolean) => void",
                  },
                  {
                    name: "onOpenChangeComplete",
                    description: "Callback fired when the open state change animation is completed.",
                    type: "() => void",
                  },
                  {
                    name: "defaultOpen",
                    description: "The default open state of the modal (uncontrolled).",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "dismissible",
                    description: "If true, the modal can be dismissed by clicking the backdrop or pressing ESC.",
                    defaultValue: "true",
                    type: "boolean",
                  },
                  {
                    name: "className",
                    description: "Additional CSS class names for the modal popup.",
                    type: "string",
                  },
                  {
                    name: "rootProps",
                    description: "Props passed to the root Dialog component.",
                    type: "Dialog.Root.Props",
                  },
                  {
                    name: "portalProps",
                    description: "Props passed to the Dialog Portal component.",
                    type: "Dialog.Portal.Props",
                  },
                  {
                    name: "backdropProps",
                    description: "Props passed to the Dialog Backdrop component.",
                    type: "Dialog.Backdrop.Props",
                  },
                  {
                    name: "popupProps",
                    description: "Props passed to the Dialog Popup component.",
                    type: "Dialog.Popup.Props",
                  },
                  {
                    name: "children",
                    description: "The content of the modal (Header, Content, Footer components).",
                    type: "ReactNode",
                  },
                ],
              },
              {
                label: "Modal.Header",
                props: [
                  {
                    name: "icon",
                    description: "Optional icon to display in the header for visual context.",
                    type: "ReactNode",
                  },
                  {
                    name: "children",
                    description: "The content of the header, typically a title using Typography component.",
                    type: "ReactNode",
                  },
                  {
                    name: "className",
                    description: "Additional CSS class names for the header.",
                    defaultValue: "-",
                    type: "string",
                  },
                ],
              },
              {
                label: "Modal.Content",
                props: [
                  {
                    name: "children",
                    description: "The main content of the modal. Automatically scrollable when content overflows.",
                    type: "ReactNode",
                  },
                  {
                    name: "className",
                    description: "Additional CSS class names for the content area.",
                    type: "string",
                  },
                ],
              },
              {
                label: "Modal.Footer",
                props: [
                  {
                    name: "children",
                    description: "The content of the footer, typically action buttons like Cancel, Confirm, Delete, etc.",
                    type: "ReactNode",
                  },
                  {
                    name: "className",
                    description: "Additional CSS class names for the footer.",
                    type: "string",
                  },
                ],
              },
              {
                label: "Modal.CloseButton",
                props: [
                  {
                    name: "className",
                    description: "Additional CSS class names for the close button.",
                    type: "string | ((state: Dialog.Close.State) => string)",
                  },
                ],
              },
            ]}
          />
          <h2 id="modal-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use for straightforward tasks like confirming routine actions, gathering simple info, or displaying brief instructions.",
              "Use when users need to choose from a long list of items or options, or when they need to fill out a long form.",
              "Use when there is no need for media (images or videos).",
              "Keep modal content focused and concise - avoid overwhelming users with too much information",
              "Include clear, action-oriented button labels that describe what will happen",
              "Place primary actions on the right side of the footer, secondary actions on the left",
              "Use icons in headers to provide visual context and improve scannability",
              "Content is automatically scrollable when it overflows the viewport height",
            ]}
          />

          <h2 id="modal-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide a clear modal title using <code>Typography</code>{" "}
                in the header for screen readers.
              </>,
              <>
                Use the <code>dismissible</code> prop appropriately - set to
                false only when user action is required.
              </>,
              <>
                Use semantic button labels and avoid generic text like "OK" or
                "Submit" - be specific about the action.
              </>,
              "Provide alternative text for any icons used in the modal header or content.",
            ]}
          />

          <h2 id="modal-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Modal component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloModal-backdrop",
                description: "Styles applied to the modal backdrop element",
                usageNotes:
                  "Use for backdrop styling including overlay color, opacity, and blur effects",
              },
              {
                cssClassName: ".ApolloModal-popup",
                description: "Styles applied to the modal popup container",
                usageNotes:
                  "Contains the modal content, handles positioning, shadow, and border radius",
              },
              {
                cssClassName: ".ApolloModal-header",
                description: "Styles applied to the modal header section",
                usageNotes:
                  "Use for header-specific styling including padding and alignment",
              },
              {
                cssClassName: ".ApolloModal-content",
                description: "Styles applied to the modal content section",
                usageNotes:
                  "Use for content area styling including padding and scrolling behavior",
              },
              {
                cssClassName: ".ApolloModal-footer",
                description: "Styles applied to the modal footer section",
                usageNotes:
                  "Use for footer styling including button alignment and spacing",
              },
              {
                cssClassName: ".ApolloModal-closeButton",
                description: "Styles applied to the close button",
                usageNotes:
                  "Use for close button positioning and appearance customization",
              },
            ]}
          />

          <h2 id="modal-examples">Examples</h2>
          <Stories title="" />

          <h2 id="modal-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <img src={actionDo} alt="modal without close button" style={{ maxWidth: "100%", padding: 24 }} />
                  ),
                  description:
                    "Provide clear information when multiple decision options are available. Ensure titles, descriptions, and buttons are unambiguous.",
                },
                negative: {
                  component: (
                    <img src={actionDont} alt="modal using multiple buttons" style={{ maxWidth: "100%", padding: 24 }} />
                  ),
                  description:
                    "Avoid using multiple buttons with the same meaning, as it causes confusion.",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    // Modal.Root props
    open: {
      control: "boolean",
      description: "Controls whether the modal is open or closed.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    onOpenChange: {
      description: "Callback fired when the open state changes.",
      table: {
        type: { summary: "(open: boolean) => void" },
      },
    },
    onOpenChangeComplete: {
      description:
        "Callback fired when the open state change animation is completed.",
      table: {
        type: { summary: "() => void" },
      },
    },
    defaultOpen: {
      control: "boolean",
      description: "The default open state of the modal (uncontrolled).",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    dismissible: {
      control: "boolean",
      description:
        "If true, the modal can be dismissed by clicking the backdrop or pressing ESC.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "true" },
      },
    },
    className: {
      control: "text",
      description: "Additional CSS class names for the modal popup.",
      table: {
        type: { summary: "string" },
      },
    },
    rootProps: {
      description: "Props passed to the root Dialog component.",
      table: {
        type: { summary: "Dialog.Root.Props" },
      },
    },
    portalProps: {
      description: "Props passed to the Dialog Portal component.",
      table: {
        type: { summary: "Dialog.Portal.Props" },
      },
    },
    backdropProps: {
      description: "Props passed to the Dialog Backdrop component.",
      table: {
        type: { summary: "Dialog.Backdrop.Props" },
      },
    },
    popupProps: {
      description: "Props passed to the Dialog Popup component.",
      table: {
        type: { summary: "Dialog.Popup.Props" },
      },
    },
  },
  args: {
    dismissible: true,
  },
}

export default meta
type Story = StoryObj<typeof Modal.Root>

/** Default Modal (demonstrates basic functionality) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview Modal with default settings. The component provides a clean, accessible dialog overlay with header, content, and footer sections.",
      },
    },
  },
  render: (args) => {
    const [open, setOpen] = useState(false)
    return (
      <div>
        <Button onClick={() => setOpen(true)}>Open Modal</Button>
        <Modal.Root
          open={open}
          onOpenChange={(open) => {
            setOpen(open)
          }}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Modal Title</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              Modal content will appear here. You can customize it however you
              want, according to the user needs. Please make sure that the
              content is clear for completing the relevant task.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setOpen(false)}>Confirm</Button>
          </Modal.Footer>
        </Modal.Root>
      </div>
    )
  },
}

/** Modal with different header configurations */
export const HeaderVariants: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Modals can have different header configurations including icons for visual context and meaning.",
      },
    },
  },
  render: (args) => {
    const [basicOpen, setBasicOpen] = useState(false)
    const [iconOpen, setIconOpen] = useState(false)
    const [noCloseOpen, setNoCloseOpen] = useState(false)

    return (
      <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
        <Button onClick={() => setBasicOpen(true)}>Basic Header</Button>
        <Button onClick={() => setIconOpen(true)}>With Icon</Button>
        <Button onClick={() => setNoCloseOpen(true)}>No Close Button</Button>

        {/* Basic Header */}
        <Modal.Root open={basicOpen} onOpenChange={setBasicOpen} {...args}>
          <Modal.Header>
            <Typography level="titleMedium">Basic Modal</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              This is a basic modal with a simple header containing just a title
              and close button.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setBasicOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setBasicOpen(false)}>Confirm</Button>
          </Modal.Footer>
        </Modal.Root>

        {/* With Icon */}
        <Modal.Root open={iconOpen} onOpenChange={setIconOpen} {...args}>
          <Modal.Header icon={<InfoCircle size={20} />}>
            <Typography level="titleMedium">Information</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              This modal includes an icon in the header to provide visual
              context and improve scannability.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={() => setIconOpen(false)}>Got it</Button>
          </Modal.Footer>
        </Modal.Root>

        {/* No Close Button */}
        <Modal.Root
          open={noCloseOpen}
          onOpenChange={setNoCloseOpen}
          dismissible={false}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Required Action</Typography>
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              This modal requires user action and cannot be dismissed by
              clicking outside or pressing ESC.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={() => setNoCloseOpen(false)}>
              Complete Action
            </Button>
          </Modal.Footer>
        </Modal.Root>
      </div>
    )
  },
}

export const FooterWithCustomButtons: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false)
    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Modal</Button>
        <Modal.Root
          open={open}
          onOpenChange={(open) => {
            setOpen(open)
          }}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Modal Title</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              Modal content will appear here, you can custom it however you
              want, according to the user needs. Please make sure that the
              content is clear for completing the relevant task.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button color="negative" onClick={() => setOpen(false)}>
              Delete
            </Button>
            <Button onClick={() => setOpen(false)}>Accept</Button>
          </Modal.Footer>
        </Modal.Root>
      </>
    )
  },
}

export const WithoutCloseButton: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false)
    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Modal</Button>
        <Modal.Root
          open={open}
          onOpenChange={(open) => {
            setOpen(open)
          }}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Modal Title</Typography>
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              Modal content will appear here, you can custom it however you
              want, according to the user needs. Please make sure that the
              content is clear for completing the relevant task.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={() => setOpen(false)}>Ok</Button>
          </Modal.Footer>
        </Modal.Root>
      </>
    )
  },
}

/** Custom styling examples */
export const CustomStyling: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Modals can be customized with CSS classes and inline styles. This example demonstrates different styling approaches.",
      },
    },
  },
  render: (args) => {
    const [basicOpen, setBasicOpen] = useState(false)
    const [coloredOpen, setColoredOpen] = useState(false)
    const [sizedOpen, setSizedOpen] = useState(false)

    return (
      <>
        <style>
          {`
            .custom-modal-purple {
              background: linear-gradient(135deg, var(--apl-alias-color-tertiary-tertiary) 0%, var(--apl-alias-color-secondary-secondary) 100%);
              border: 2px solid var(--apl-alias-color-secondary-secondary);
              box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            }

            .custom-modal-purple .ApolloModal-header {
              color: white;
            }

            .custom-modal-purple .ApolloModal-content {
              color: white;
            }

            .custom-modal-rounded {
              border-radius: 24px;
              border: 3px solid var(--apl-alias-color-outline-and-border-outline);
            }

            .custom-modal-large {
              min-width: 600px;
              max-width: 80vw;
            }
          `}
        </style>

        <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
          <Button onClick={() => setBasicOpen(true)}>Basic Custom Style</Button>
          <Button onClick={() => setColoredOpen(true)}>Colored Modal</Button>
          <Button onClick={() => setSizedOpen(true)}>Custom Size</Button>
        </div>

        {/* Basic Custom Style */}
        <Modal.Root
          open={basicOpen}
          onOpenChange={setBasicOpen}
          className="custom-modal-rounded"
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Custom Rounded Modal</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              This modal has custom border radius and background styling applied
              via CSS classes.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={() => setBasicOpen(false)}>OK</Button>
          </Modal.Footer>
        </Modal.Root>

        {/* Colored Modal */}
        <Modal.Root
          open={coloredOpen}
          onOpenChange={setColoredOpen}
          className="custom-modal-purple"
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Purple Gradient Modal</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              This modal demonstrates custom background gradients and color
              schemes.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={() => setColoredOpen(false)}>OK</Button>
          </Modal.Footer>
        </Modal.Root>

        {/* Custom Size Modal */}
        <Modal.Root
          open={sizedOpen}
          onOpenChange={setSizedOpen}
          className="custom-modal-large"
          style={{
            border: "2px dashed #4299e1",
            backgroundColor: "#ebf8ff",
          }}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Large Custom Modal</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge" style={{ marginBottom: "16px" }}>
              This modal combines CSS classes with inline styles for maximum
              customization.
            </Typography>
            <Typography level="bodyMedium" style={{ color: "#2b6cb0" }}>
              You can use both className prop and style prop to achieve the
              desired appearance.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={() => setSizedOpen(false)}>Ok</Button>
          </Modal.Footer>
        </Modal.Root>
      </>
    )
  },
}

/** Modal with form content */
export const WithForm: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Modals can contain form elements for data collection. This example shows proper form layout and validation.",
      },
    },
  },
  render: (args) => {
    const [open, setOpen] = useState(false)
    const [name, setName] = useState("")
    const [email, setEmail] = useState("")

    const handleSubmit = () => {
      console.log("Form submitted:", { name, email })
      setOpen(false)
      setName("")
      setEmail("")
    }

    const handleCancel = () => {
      setOpen(false)
      setName("")
      setEmail("")
    }

    return (
      <>
        <Button onClick={() => setOpen(true)}>Add Contact</Button>
        <Modal.Root
          open={open}
          onOpenChange={(open) => {
            if (!open) handleCancel()
          }}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Add New Contact</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <div className="flex flex-col gap-4">
              <Typography level="bodyMedium">
                Please fill in the contact information below:
              </Typography>
              <Input
                label="Full Name"
                placeholder="Enter full name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
              <Input
                label="Email Address"
                placeholder="Enter email address"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
          </Modal.Content>
          <Modal.Footer>
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={!name || !email}>
              Add Contact
            </Button>
          </Modal.Footer>
        </Modal.Root>
      </>
    )
  },
}

/** Confirmation modal for destructive actions */
export const ConfirmationModal: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Confirmation modals are used for destructive or irreversible actions. They should clearly explain the consequences and use appropriate visual cues.",
      },
    },
  },
  render: (args) => {
    const [deleteOpen, setDeleteOpen] = useState(false)
    const [logoutOpen, setLogoutOpen] = useState(false)

    return (
      <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
        <Button color="negative" onClick={() => setDeleteOpen(true)}>
          Delete Account
        </Button>
        <Button variant="outline" onClick={() => setLogoutOpen(true)}>
          Sign Out
        </Button>

        {/* Delete Confirmation */}
        <Modal.Root open={deleteOpen} onOpenChange={setDeleteOpen} {...args}>
          <Modal.Header
            icon={
              <Warning
                size={20}
                style={{ color: "var(--apl-alias-color-error-error)" }}
              />
            }
          >
            <Typography level="titleMedium">Delete Account</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <div className="flex flex-col gap-3">
              <Typography level="bodyLarge">
                Are you sure you want to delete your account?
              </Typography>
              <Typography
                level="bodyMedium"
                style={{ color: "var(--apl-alias-color-secondary-secondary)" }}
              >
                This action cannot be undone. All your data, including projects,
                files, and settings will be permanently deleted.
              </Typography>
            </div>
          </Modal.Content>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setDeleteOpen(false)}>
              Cancel
            </Button>
            <Button color="negative" onClick={() => setDeleteOpen(false)}>
              Delete Account
            </Button>
          </Modal.Footer>
        </Modal.Root>

        {/* Logout Confirmation */}
        <Modal.Root open={logoutOpen} onOpenChange={setLogoutOpen} {...args}>
          <Modal.Header>
            <Typography level="titleMedium">Sign Out</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              Are you sure you want to sign out? Any unsaved changes will be
              lost.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setLogoutOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setLogoutOpen(false)}>Sign Out</Button>
          </Modal.Footer>
        </Modal.Root>
      </div>
    )
  },
}

/** Information and notification modals */
export const InformationModal: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Information modals are used to display important messages, notifications, or status updates to users.",
      },
    },
  },
  render: (args) => {
    const [successOpen, setSuccessOpen] = useState(false)
    const [errorOpen, setErrorOpen] = useState(false)
    const [infoOpen, setInfoOpen] = useState(false)

    return (
      <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
        <Button onClick={() => setSuccessOpen(true)}>Success Message</Button>
        <Button onClick={() => setErrorOpen(true)}>Error Message</Button>
        <Button onClick={() => setInfoOpen(true)}>Information</Button>

        {/* Success Modal */}
        <Modal.Root open={successOpen} onOpenChange={setSuccessOpen} {...args}>
          <Modal.Header
            icon={
              <CheckCircle
                size={20}
                style={{ color: "var(--apl-alias-color-success-success)" }}
              />
            }
          >
            <Typography level="titleMedium">Success</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              Your account has been successfully created! You can now start
              using all the features.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={() => setSuccessOpen(false)}>Continue</Button>
          </Modal.Footer>
        </Modal.Root>

        {/* Error Modal */}
        <Modal.Root open={errorOpen} onOpenChange={setErrorOpen} {...args}>
          <Modal.Header
            icon={
              <ExclamationCircle
                size={20}
                style={{ color: "var(--apl-alias-color-error-error)" }}
              />
            }
          >
            <Typography level="titleMedium">Error</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <div className="flex flex-col gap-3">
              <Typography level="bodyLarge">
                Unable to save your changes
              </Typography>
              <Typography
                level="bodyMedium"
                style={{ color: "var(--apl-alias-color-secondary-secondary)" }}
              >
                There was a problem connecting to the server. Please check your
                internet connection and try again.
              </Typography>
            </div>
          </Modal.Content>
          <Modal.Footer>
            <Button
              variant="outline"
              color="negative"
              onClick={() => setErrorOpen(false)}
            >
              Cancel
            </Button>
            <Button color="negative" onClick={() => setErrorOpen(false)}>
              Try Again
            </Button>
          </Modal.Footer>
        </Modal.Root>

        {/* Info Modal */}
        <Modal.Root open={infoOpen} onOpenChange={setInfoOpen} {...args}>
          <Modal.Header
            icon={
              <InfoCircle
                size={20}
                style={{ color: "var(--apl-alias-color-primary-primary)" }}
              />
            }
          >
            <Typography level="titleMedium">New Feature Available</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <div className="flex flex-col gap-3">
              <Typography level="bodyLarge">
                We've added a new dashboard feature to help you track your
                progress.
              </Typography>
              <Typography
                level="bodyMedium"
                style={{ color: "var(--apl-alias-color-secondary-secondary)" }}
              >
                You can access it from the main navigation menu. Would you like
                to take a quick tour?
              </Typography>
            </div>
          </Modal.Content>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setInfoOpen(false)}>
              Maybe Later
            </Button>
            <Button onClick={() => setInfoOpen(false)}>Take Tour</Button>
          </Modal.Footer>
        </Modal.Root>
      </div>
    )
  },
}

/** Scrollable content demonstration */
export const ScrollableContent: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Modal content can be made scrollable when it exceeds the available space. This example demonstrates both scrollable and non-scrollable content behavior.",
      },
    },
  },
  render: (args) => {
    const [scrollableOpen, setScrollableOpen] = useState(false)
    const [nonScrollableOpen, setNonScrollableOpen] = useState(false)
    const longContent = Array.from({ length: 20 }, (_, i) => (
      <Typography key={i} level="bodyMedium" style={{ marginBottom: "12px" }}>
        This is paragraph {i + 1} of the long content. Lorem ipsum dolor sit
        amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut
        labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
        exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
      </Typography>
    ))

    return (
      <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
        <Button onClick={() => setScrollableOpen(true)}>
          Scrollable Content
        </Button>
        <Button onClick={() => setNonScrollableOpen(true)}>
          Non-Scrollable Content
        </Button>

        {/* Scrollable Modal */}
        <Modal.Root
          open={scrollableOpen}
          onOpenChange={setScrollableOpen}
          style={{ maxWidth: "500px" }}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">
              Scrollable Content Modal
            </Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <div style={{ padding: "16px" }}>
              <Typography level="bodyLarge" style={{ marginBottom: "16px" }}>
                This modal has scrollable content. When the content exceeds the
                available space, a scrollbar will appear allowing users to
                scroll through the content.
              </Typography>
              {longContent}
            </div>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={() => setScrollableOpen(false)}>Close</Button>
          </Modal.Footer>
        </Modal.Root>

        {/* Non-Scrollable Modal */}
        <Modal.Root
          open={nonScrollableOpen}
          onOpenChange={setNonScrollableOpen}
          style={{ maxWidth: "500px" }}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">
              Non-Scrollable Content Modal
            </Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <div style={{ padding: "16px" }}>
              <Typography level="bodyLarge" style={{ marginBottom: "16px" }}>
                This modal has non-scrollable content. The content will expand
                the modal height as needed (up to viewport limits).
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: "12px" }}>
                This is a shorter example to demonstrate non-scrollable
                behavior.
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: "12px" }}>
                The modal will size itself to fit the content without showing
                scrollbars.
              </Typography>
            </div>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={() => setNonScrollableOpen(false)}>Close</Button>
          </Modal.Footer>
        </Modal.Root>
      </div>
    )
  },
}
