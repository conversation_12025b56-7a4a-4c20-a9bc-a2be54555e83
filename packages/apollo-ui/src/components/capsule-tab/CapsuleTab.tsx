import classNames from "classnames"

import { isTextElement } from "../../utils"
import { Typography } from "../typography"
import styles from "./CapsuleTab.module.css"
import type { CapsuleTabProps } from "./CapsuleTabProps"

export function CapsuleTab({
  tabs,
  className,
  selectedIndex,
  onSelect,
  ref,
}: CapsuleTabProps) {
  return (
    <div
      className={classNames("ApolloCapsuleTab-root", styles.capsuletab, styles.root, className)}
      ref={ref}
    >
      <div
        className={classNames("ApolloCapsuleTab-container", styles.container)}
      >
        {tabs?.map(({ id, label }, index) => (
          <button
            className={classNames("ApolloCapsuleTab-item", styles.item, {
              [`ApolloCapsuleTab-itemSelected ${styles.itemSelected}`]:
                selectedIndex === index,
            })}
            key={id}
            onClick={() => onSelect?.(index)}
            type="button"
          >
            {isTextElement(label) ? (
              <Typography
                className={classNames(
                  "ApolloCapsuleTab-itemText",
                  styles.itemText
                )}
                level="titleSmall"
              >
                {label}
              </Typography>
            ) : (
              label
            )}
          </button>
        ))}
      </div>
    </div>
  )
}
