/* eslint-disable @typescript-eslint/no-explicit-any */
"use client"

import {
  ComponentType,
  forwardRef,
  useCallback,
  useMemo,
  useRef,
  useState,
  type ChangeEventHandler,
} from "react"
import {
  getMonth,
  getYear,
  isAfter,
  isBefore,
  isValid,
  setMonth,
  setYear,
  startOfDay,
} from "date-fns"
import { enUS, th } from "date-fns/locale"
import DatePickerBase, {
  DatePickerProps as BaseDatePickerProps,
} from "react-datepicker"

import { Button } from "../button"
import { Calendar } from "../common/Calendar"
import { Left } from "../common/Left"
import { Right } from "../common/Right"
import { Input } from "../input"
import { Typography } from "../typography"
import styles from "./date-picker.module.css"
import type { DatePickerProps, DatePickerViewMode } from "./DatePickerProps"
import { defaultFormatMap, format as formatDate, parse } from "./utils"

const DatePickerBaseComponent =
  DatePickerBase as unknown as ComponentType<BaseDatePickerProps>

const DatePicker = forwardRef<DatePickerBase, DatePickerProps>(
  function DatePickerRoot(props, ref) {
    const {
      value: selectedDate,
      onChange,
      onBlur,
      label,
      helperText,
      error,
      className,
      placeholder,
      locale: localeProps = "th",
      format,
      inputProps,
      disabled,
      era = "bd",
      excludeDates,
      showMonthYearPicker,
      showYearPicker,
      onViewModeChange,
      startDate,
      isRange,
      endDate,
      hideCalendarMonth,
      hideCalendarYear,
      shouldCloseOnSelect,
      shouldBackToDateViewAfterSelect = true,
      portal,
      ...reactDatePickerProps
    } = props

    const defaultViewMode = useMemo(() => {
      if (showMonthYearPicker) {
        return "month"
      } else if (showYearPicker) {
        return "year"
      }
      return "date"
    }, [showMonthYearPicker, showYearPicker])

    const isBuddhistEra = format ? format?.includes("bb") : era === "bd"
    const displayEra = isBuddhistEra ? "bd" : "ad"
    const defaultFormat = defaultFormatMap?.[displayEra]
    const locale = localeProps === "th" ? th : enUS

    const [typingValue, setTypingValue] = useState<string | null>(null)
    const calendarNavigationFns = useRef({
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      changeMonth: (_: number) => {},
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      changeYear: (_: number) => {},
    })

    const [viewMode, setViewMode] =
      useState<DatePickerViewMode>(defaultViewMode)

    const [isDatePicker, isMonthPicker, isYearPicker] = useMemo(
      () => [viewMode === "date", viewMode === "month", viewMode === "year"],
      [viewMode]
    )

    const handleChangeViewMode = useCallback(
      (mode: DatePickerViewMode) => {
        onViewModeChange?.(mode)
        setViewMode(mode)
      },
      [onViewModeChange]
    )

    const getParsedValue = useCallback(
      (selectedDate: Date | null, currentValue?: Date | null) => {
        if (isMonthPicker || isYearPicker) {
          return currentValue ?? null
        }
        return selectedDate
      },
      [isMonthPicker, isYearPicker]
    )

    const validateLimitDate = (date: Date) => {
      if (date && (props?.minDate || props?.maxDate)) {
        if (props?.minDate && isBefore(date, props.minDate)) {
          return props.minDate
        }

        if (props?.maxDate && isAfter(date, props.maxDate)) {
          return props.maxDate
        }
      }
      return date
    }

    const handleChange = (value: Date | null, event: any) => {
      const isWithTimeSelectVisibleOnChange =
        reactDatePickerProps?.showTimeSelect && !event
      if (isWithTimeSelectVisibleOnChange || event.type === "click") {
        const currentValue = selectedDate ?? startOfDay(new Date())
        let newValue: Date | null = value

        if (value) {
          if (isMonthPicker) {
            const newMonth = getMonth(value)
            const newYear = getYear(value)
            newValue = setYear(setMonth(currentValue, newMonth), newYear)

            if (shouldBackToDateViewAfterSelect) {
              handleChangeViewMode("date")
            }
          } else if (isYearPicker) {
            const newYear = getYear(value)
            newValue = setYear(currentValue, newYear)
            if (shouldBackToDateViewAfterSelect) {
              handleChangeViewMode("date")
            }
          }
        }

        if (newValue) {
          newValue = validateLimitDate(newValue)
          calendarNavigationFns.current.changeMonth(getMonth(newValue))
          calendarNavigationFns.current.changeYear(getYear(newValue))
        }

        const onChangeFn = onChange as (value: Date | null) => void
        onChangeFn?.(newValue)
      }
    }

    const handleRangeChange = (
      rangeValue: [Date | null, Date | null],
      event: any
    ) => {
      if (event.type === "click") {
        const [newStartDate, newEndDate] = rangeValue
        const parsedStartDate = getParsedValue(newStartDate, startDate)
        const parsedEndDate = getParsedValue(newEndDate, endDate)

        if (isDatePicker) {
          const onChangeFn = onChange as (
            value: [Date | null, Date | null]
          ) => void
          const validStartDate = parsedStartDate
            ? validateLimitDate(parsedStartDate)
            : parsedStartDate
          const validEndDate = parsedEndDate
            ? validateLimitDate(parsedEndDate)
            : parsedEndDate
          onChangeFn?.([validStartDate, validEndDate])
        } else {
          if (shouldBackToDateViewAfterSelect) {
            handleChangeViewMode("date")
          }
        }
      }
    }

    const displayFormat = useMemo(
      () => format ?? defaultFormat?.fullDate,
      [defaultFormat?.fullDate, format]
    )

    const displayValue = useMemo(() => {
      const formatFn = (date?: Date | null) =>
        date
          ? formatDate(date, displayFormat, {
              locale,
            })
          : ""

      if (isRange) {
        return `${formatFn(startDate)}${startDate ? " - " : ""}${formatFn(
          endDate
        )}`
      }

      return formatFn(selectedDate)
    }, [displayFormat, endDate, isRange, locale, selectedDate, startDate])

    const handleFocus = () => {
      setTypingValue(displayValue)
    }

    const handleBlur = () => {
      onBlur?.()
    }

    const handleRawChange: ChangeEventHandler<HTMLInputElement> = (event) => {
      const value = event.target.value
      setTypingValue(value)

      if (event.type !== "change") {
        return
      }

      if (isRange) {
        const splittedValues = value?.split("-")?.map((str) => str.trim())
        const rawStartDate = splittedValues?.[0]
        const rawEndDate = splittedValues?.[1]

        const onChangeRangeFN = (startDate: Date, endDate: Date | null) => {
          const onChangeFN = onChange as (value: (Date | null)[]) => void
          onChangeFN([startDate, endDate])

          calendarNavigationFns.current.changeMonth(
            getMonth(endDate ?? startDate)
          )
          calendarNavigationFns.current.changeYear(
            getYear(endDate ?? startDate)
          )
        }

        const startDate = parse(rawStartDate, displayFormat, {
          locale,
        }) as Date

        const endDate = parse(rawEndDate, displayFormat, {
          locale,
        }) as Date

        const isStartDateValid = isValid(startDate)
        const isEndDateValid = isValid(endDate)

        const isEndGreaterThanStart = isAfter(endDate, startDate)

        if (!isStartDateValid && !isEndDateValid) {
          onChange?.([null, null])
        }

        if (isStartDateValid && isEndDateValid && isEndGreaterThanStart) {
          const validStartDate = validateLimitDate(startDate)
          const validEndDate = validateLimitDate(endDate)
          onChangeRangeFN(validStartDate, validEndDate)
        } else if (isValid(startDate)) {
          const validStartDate = validateLimitDate(startDate)
          onChangeRangeFN(validStartDate, null)
        }
      } else {
        const onChangeFN = onChange as (value: Date | null) => void

        const newValue = parse(value, displayFormat, {
          locale,
        })

        const isEmptyValue = ["", null].includes(value)
        if (isEmptyValue) {
          onChangeFN(null)
          return
        }
        const isValueValid = isValid(newValue)

        if (isValueValid) {
          let validValue = newValue as Date

          if (newValue) {
            validValue = validateLimitDate(validValue)
          }

          onChangeFN(validValue)

          calendarNavigationFns.current.changeMonth(getMonth(validValue))
          calendarNavigationFns.current.changeYear(getYear(validValue))
        }
      }
    }

    const endDecoratorClassName = disabled
      ? `${styles.inputEndDecorator} ${styles.disabled}`
      : styles.inputEndDecorator

    return (
      <DatePickerBaseComponent
        customInput={
          <Input
            endDecorator={
              <Calendar
                width={18}
                height={18}
                className={endDecoratorClassName}
              />
            }
            error={error}
            fullWidth
            helperText={helperText}
            label={label}
            placeholder={placeholder}
            {...inputProps}
            value={typingValue ?? displayValue}
          />
        }
        disabled={disabled}
        excludeDates={isDatePicker ? excludeDates : undefined}
        locale={locale}
        onCalendarClose={() => {
          setTypingValue(null)
          handleChangeViewMode(defaultViewMode)
          handleBlur?.()
        }}
        onChange={isRange ? handleRangeChange : handleChange}
        onChangeRaw={handleRawChange}
        onFocus={handleFocus}
        placeholderText={placeholder}
        ref={ref}
        showDateSelect
        wrapperClassName={
          className
            ? `${styles.datePickerRoot} ${className}`
            : styles.datePickerRoot
        }
        {...reactDatePickerProps}
        renderCustomHeader={({
          date: displayDate,
          decreaseMonth,
          increaseMonth,
          decreaseYear,
          increaseYear,
          changeMonth,
          changeYear,
          prevMonthButtonDisabled,
          nextMonthButtonDisabled,
        }) => {
          calendarNavigationFns.current.changeMonth = changeMonth
          calendarNavigationFns.current.changeYear = changeYear
          return (
            <div className={styles.datePickerCalendarHeader}>
              {!isMonthPicker ? (
                <Button
                  className={styles.datePickerPrevMonthButton}
                  disabled={prevMonthButtonDisabled}
                  onClick={isYearPicker ? decreaseYear : decreaseMonth}
                  variant="text"
                >
                  <Left width={14} height={14} />
                </Button>
              ) : null}
              <div className="flex flex-row justify-center items-center">
                {!hideCalendarMonth ? (
                  <Button
                    className={styles.datePickerMonthButton}
                    onClick={() => {
                      if (isMonthPicker) {
                        handleChangeViewMode(defaultViewMode)
                      } else {
                        handleChangeViewMode("month")
                      }
                    }}
                    variant="text"
                  >
                    <Typography level="titleLarge">
                      {formatDate(displayDate, defaultFormat?.header?.month, {
                        locale,
                      })}
                      ,
                    </Typography>
                  </Button>
                ) : null}
                {!hideCalendarYear ? (
                  <Button
                    className={styles.datePickerYearButton}
                    onClick={() => {
                      if (isYearPicker) {
                        handleChangeViewMode(defaultViewMode)
                      } else {
                        handleChangeViewMode("year")
                      }
                    }}
                    variant="text"
                  >
                    <Typography level="titleLarge">
                      {formatDate(displayDate, defaultFormat?.header?.year, {
                        locale,
                      })}
                    </Typography>
                  </Button>
                ) : null}
              </div>
              {!isMonthPicker ? (
                <Button
                  className={styles.datePickerNextMonthButton}
                  disabled={nextMonthButtonDisabled}
                  onClick={isYearPicker ? increaseYear : increaseMonth}
                  variant="text"
                >
                  <Right width={14} height={14} />
                </Button>
              ) : null}
            </div>
          )
        }}
        renderDayContents={(_, date) => (
          <Typography className={styles.datePickerDay} level="bodyMedium">
            {formatDate(date, "d")}
          </Typography>
        )}
        renderMonthContent={(_, shortMonth) => (
          <Typography className={styles.datePickerMonth} level="bodyMedium">
            {shortMonth}
          </Typography>
        )}
        renderYearContent={(year) => (
          <Typography className={styles.datePickerYear} level="bodyMedium">
            {isBuddhistEra ? year + 543 : year}
          </Typography>
        )}
        selected={isRange ? startDate : selectedDate}
        {...((isRange
          ? {
              selectsRange: true,
            }
          : {}) as any)}
        endDate={endDate}
        inline={false}
        shouldCloseOnSelect={shouldCloseOnSelect ?? isDatePicker}
        showMonthYearPicker={showMonthYearPicker ?? viewMode === "month"}
        showPopperArrow={false}
        showYearPicker={showYearPicker ?? viewMode === "year"}
        startDate={startDate}
        value={typingValue ?? displayValue}
        portalId={
          reactDatePickerProps?.portalId ??
          (portal ? "apollo-portal-root" : undefined)
        }
      />
    )
  }
)

DatePicker.displayName = "DatePicker"

export default DatePicker
